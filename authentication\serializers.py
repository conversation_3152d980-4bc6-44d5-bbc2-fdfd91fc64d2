from rest_framework import serializers
from django.utils import timezone
from datetime import timedelta
import logging
from rest_framework.exceptions import ValidationError

from authentication.models import TcdAppMember, TcdUserConsult, TcdUserConsultTeam, TcdActionLog, TcdAppMasMemberType, TcdAppMasGovernmentSector, TcdAppMasMinistry, TcdAppMasDepartment
from authentication.utils import verify_password
from authentication.constants import (
    MemberStatus,
    LoginAttempts,
    ErrorMessages,
    ConsultantVerificationStatus,
    ConsultantType,
    OTPPurpose
)
from utils.i18n.th import ERROR_MESSAGES
from utils.response import service_error_response

# Setup logging
logger = logging.getLogger(__name__)


class MemberLoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, style={'input_type': 'password'})
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        logger.info(f"MemberLoginSerializer.validate called for username: {username}")
        
        if username and password:
            try:
                # ค้นหา member จากตาราง tcd_app_member
                member = TcdAppMember.objects.get(username=username)
                logger.info(f"Found member: {username}, current checklogin: {member.checklogin}, lockout_end_date: {member.lockout_end_date}")
                
                # ตรวจสอบ lockout ก่อนตรวจสอบอื่นๆ
                if member.lockout_end_date and member.lockout_end_date > timezone.now():
                    logger.warning(f"Member {username} is locked out until: {member.lockout_end_date}")
                    time_remaining = member.lockout_end_date - timezone.now()
                    logger.warning(f"SERIALIZER: Account is locked! Remaining time: {time_remaining.total_seconds()} seconds")
                    
                    # ไม่ต้อง raise ValidationError แล้ว แค่คืนค่า attrs โดยมี member ที่มี lockout_end_date
                    attrs['member'] = member
                    attrs['error_code'] = 1002  # Account locked
                    return attrs
                
                # ตรวจสอบสถานะ member
                if member.status == MemberStatus.DELETED:
                    logger.warning(f"Member {username} has deleted status: {member.status}")
                    attrs['member'] = member
                    attrs['error_code'] = 1010  # Account deleted
                    return attrs
                elif not MemberStatus.is_active(member.status):
                    logger.warning(f"Member {username} has inactive status: {member.status}")

                    # แทนที่จะใช้ ValidationError ให้ส่ง error_code กลับไป
                    error_code = 1002 if member.status == 'L' else 1003  # Account locked or inactive
                    attrs['member'] = member
                    attrs['error_code'] = error_code
                    return attrs
                
                # ตรวจสอบรหัสผ่าน - เข้ารหัสด้วย MD5 แล้วเปรียบเทียบ
                logger.info(f"Verifying password for member: {username}")
                if verify_password(password, member.password):
                    logger.info(f"Password verification successful for member: {username}")
                    
                    # ล้าง checklogin เมื่อ login สำเร็จ
                    if member.checklogin > 0:
                        member.checklogin = 0
                        member.lockout_end_date = None
                        member.save()
                        logger.info(f"Reset checklogin to 0 for successful login: {username}")
                    
                    attrs['member'] = member
                    return attrs
                else:
                    logger.warning(f"Password verification failed for member: {username}")
                    
                    # เก็บค่า checklogin เดิมก่อนเพิ่ม
                    old_checklogin = member.checklogin
                    
                    # เพิ่มจำนวนครั้งที่ login ผิด
                    member.checklogin += 1
                    
                    logger.info(f"Incrementing checklogin for {username}: {old_checklogin} -> {member.checklogin}")
                    
                    # ตรวจสอบว่าควรล็อคบัญชีหรือไม่
                    will_be_locked = LoginAttempts.is_locked_out(member.checklogin)
                    
                    if will_be_locked:
                        # ตั้ง lockout_end_date และ reset checklogin เป็น 0
                        member.lockout_end_date = timezone.now() + timedelta(seconds=LoginAttempts.LOCKOUT_SECONDS)
                        member.checklogin = 0
                        logger.warning(f"Account {username} locked until: {member.lockout_end_date}, checklogin reset to 0")
                    else:
                        # ถ้ายังไม่ล็อค ให้ตั้ง lockout_end_date เป็น None
                        member.lockout_end_date = None
                    
                    # บันทึกการเปลี่ยนแปลง
                    try:
                        member.save()
                        logger.info(f"Successfully saved member data for {username} - checklogin: {member.checklogin}, lockout_end_date: {member.lockout_end_date}")
                        
                        # ตรวจสอบว่าบันทึกสำเร็จหรือไม่
                        member.refresh_from_db()
                        logger.info(f"Verified data after save - checklogin: {member.checklogin}, lockout_end_date: {member.lockout_end_date}")
                        
                    except Exception as save_error:
                        logger.error(f"Failed to save member data for {username}: {str(save_error)}")
                    
                    # แทนที่จะใช้ ValidationError ให้คืนค่า attrs ที่มี member และ error_code
                    attrs['member'] = member
                    
                    # เพิ่ม error_code ให้ view ตรวจสอบได้
                    if will_be_locked:
                        logger.warning(f"Account {username} is now locked due to too many failed attempts")
                        attrs['error_code'] = 1002  # Account locked
                    else:
                        logger.info(f"Invalid credentials for {username}")
                        attrs['error_code'] = 1001  # Invalid credentials
                    
                    return attrs
            except TcdAppMember.DoesNotExist:
                logger.warning(f"Member not found: {username}")
                attrs['error_code'] = 1001  # Invalid username or password
                return attrs
            except Exception as e:
                logger.error(f"Unexpected error in MemberLoginSerializer.validate: {str(e)}")
                attrs['error_code'] = 1001  # Invalid username or password
                return attrs
        else:
            logger.warning("Missing username or password")
            attrs['error_code'] = 2001  # Missing credentials
            return attrs


class ConsultantLoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, style={'input_type': 'password'})
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            try:
                # ค้นหา consultant จากตาราง tcd_user_consult
                consultant = TcdUserConsult.objects.get(username=username)
                
                # ตรวจสอบสถานะการยืนยัน (verify)
                if consultant.verify and not ConsultantVerificationStatus.is_verified(consultant.verify):
                    raise serializers.ValidationError({
                        'non_field_errors': [{
                            'status': 'error',
                            'code': 'account_unverified',
                            'message': ErrorMessages.get_verification_message(consultant.verify)
                        }]
                    })
                
                # ตรวจสอบรหัสผ่าน - เข้ารหัสด้วย MD5 แล้วเปรียบเทียบ
                if verify_password(password, consultant.password):
                    attrs['consultant'] = consultant
                    return attrs
                else:
                    raise serializers.ValidationError({
                        'non_field_errors': [{
                            'status': 'error',
                            'code': 'invalid_credentials',
                            'message': ErrorMessages.INVALID_CREDENTIALS
                        }]
                    })
                    
            except TcdUserConsult.DoesNotExist:
                raise serializers.ValidationError({
                    'non_field_errors': [{
                        'status': 'error',
                        'code': 'invalid_credentials',
                        'message': ErrorMessages.INVALID_CREDENTIALS
                    }]
                })
        else:
            raise serializers.ValidationError({
                'non_field_errors': [{
                    'status': 'error',
                    'code': 'missing_credentials',
                    'message': ErrorMessages.MISSING_CREDENTIALS
                }]
            })


class MemberSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล Member (ทุกฟิลด์ยกเว้น password)
    """
    status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdAppMember
        fields = [
            'id', 'name', 'first_name', 'last_name', 'email', 'phone',
            'identity_card_no', 'src', 'fb_id', 'google_id', 'apple_id',
            'username', 'app_mas_member_type', 'app_mas_government_sector',
            'app_mas_government_sector_other', 'app_mas_ministry', 'app_mas_ministry_other',
            'app_mas_department', 'app_mas_department_other', 'website', 'create_date',
            'token_app', 'is_notification', 'checklogin', 'lockout_end_date',
            'status', 'status_display', 'lang'
        ]
        read_only_fields = ['id', 'create_date', 'checklogin', 'lockout_end_date']
        
    def get_status_display(self, obj):
        """แปลง status ให้เป็นข้อความที่เข้าใจได้"""
        status_dict = dict(MemberStatus.CHOICES)
        return status_dict.get(obj.status, 'ไม่ระบุ')


class ConsultantSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล Consultant (ทุกฟิลด์ยกเว้น password)
    """
    verify_display = serializers.SerializerMethodField()
    consult_type_display = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdUserConsult
        fields = [
            'id', 'consult_type', 'consult_type_display', 'corporate_type_id',
            'username', 'email', 'email_second', 'phone', 'phone_second', 'src',
            'maker_name', 'maker_phone', 'maker_email', 'pw', 'verify', 'verify_display',
            'reset_phone', 'reset_phone_date', 'token_app', 'is_notification',
            'score', 'lang', 'is_active_matching'
        ]
        read_only_fields = ['id', 'score', 'reset_phone', 'reset_phone_date']
        
    def get_verify_display(self, obj):
        """แปลง verify status ให้เป็นข้อความที่เข้าใจได้"""
        verify_dict = dict(ConsultantVerificationStatus.CHOICES)
        return verify_dict.get(obj.verify, 'ไม่ระบุ')
        
    def get_consult_type_display(self, obj):
        """แปลง consult_type ให้เป็นข้อความที่เข้าใจได้"""
        consult_type_dict = dict(ConsultantType.CHOICES)
        return consult_type_dict.get(obj.consult_type, 'ไม่ระบุ')


class CustomUserDetailsSerializer(serializers.Serializer):
    """
    Serializer สำหรับแสดงข้อมูลผู้ใช้ทั้ง member และ consultant
    """
    id = serializers.IntegerField(read_only=True)
    username = serializers.CharField(read_only=True)
    email = serializers.EmailField(read_only=True)
    user_type = serializers.CharField(read_only=True)
    first_name = serializers.CharField(read_only=True, required=False, allow_null=True, allow_blank=True)
    last_name = serializers.CharField(read_only=True, required=False, allow_null=True, allow_blank=True)
    phone = serializers.CharField(read_only=True, required=False, allow_null=True, allow_blank=True)
    
    class Meta:
        fields = ('id', 'username', 'email', 'user_type', 'first_name', 'last_name', 'phone')
        
    def to_representation(self, instance):
        """
        Handle both built-in User model and our custom models
        """
        # Get basic representation
        data = super().to_representation(instance)
        
        # Add user_type if not present
        if 'user_type' not in data:
            data['user_type'] = getattr(instance, 'user_type', 'unknown')
            
        # If this is one of our custom models, ensure we have the required fields
        if hasattr(instance, 'user_type'):
            # Set is_authenticated to ensure it's recognized as authenticated
            if not data.get('is_authenticated', False):
                data['is_authenticated'] = True
                
            # Add additional fields based on the user type
            if instance.user_type == 'member':
                # Add member-specific fields
                data.update({
                    'identity_card_no': getattr(instance, 'identity_card_no', None),
                    'status': getattr(instance, 'status', None),
                })
            elif instance.user_type == 'consultant':
                # Add consultant-specific fields
                data.update({
                    'consult_type': getattr(instance, 'consult_type', None),
                    'verify': getattr(instance, 'verify', None),
                })
                
        return data 


class TcdUserConsultTeamSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูลทีมของ Consultant
    """
    user_consult_username = serializers.CharField(source='user_consult.username', read_only=True)
    user_consult_email = serializers.CharField(source='user_consult.email', read_only=True)
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdUserConsultTeam
        fields = [
            'id', 'user_consult', 'user_consult_username', 'user_consult_email',
            'first_name', 'last_name', 'full_name', 'identity_card_no', 'is_admin'
        ]
        read_only_fields = ['id']
        
    def get_full_name(self, obj):
        """รวมชื่อและนามสกุลเป็นชื่อเต็ม"""
        return f"{obj.first_name} {obj.last_name}".strip()
        
    def validate_identity_card_no(self, value):
        """ตรวจสอบรูปแบบเลขบัตรประชาชน"""
        if value and len(value) != 13:
            raise serializers.ValidationError("เลขบัตรประชาชนต้องมี 13 หลัก")
        if value and not value.isdigit():
            raise serializers.ValidationError("เลขบัตรประชาชนต้องเป็นตัวเลขเท่านั้น")
        return value


class TcdActionLogSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล Action Log
    """
    action_date_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdActionLog
        fields = [
            'id', 'action_date', 'action_date_formatted', 'action_log', 
            'remark', 'user_name', 'consult_name', 'ip_address'
        ]
        read_only_fields = ['id', 'action_date']
        
    def get_action_date_formatted(self, obj):
        """แปลงวันที่ให้เป็นรูปแบบที่อ่านง่าย"""
        if obj.action_date:
            return obj.action_date.strftime('%d/%m/%Y %H:%M:%S')
        return None
        
    def validate_action_log(self, value):
        """ตรวจสอบว่า action_log ไม่เป็นค่าว่าง"""
        if not value or not value.strip():
            raise serializers.ValidationError("กรุณาระบุ Action Log")
        return value.strip()
        
    def validate_user_name(self, value):
        """ตรวจสอบว่า user_name ไม่เป็นค่าว่าง"""
        if not value or not value.strip():
            raise serializers.ValidationError("กรุณาระบุชื่อผู้ใช้")
        return value.strip()


class TcdActionLogCreateSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับสร้าง Action Log ใหม่ (ไม่ต้องระบุ action_date)
    """
    
    class Meta:
        model = TcdActionLog
        fields = [
            'action_log', 'remark', 'user_name', 'consult_name', 'ip_address'
        ]
        
    def create(self, validated_data):
        """สร้าง Action Log ใหม่พร้อมกับ action_date ปัจจุบัน"""
        validated_data['action_date'] = timezone.now()
        return super().create(validated_data)
        
    def validate_action_log(self, value):
        """ตรวจสอบว่า action_log ไม่เป็นค่าว่าง"""
        if not value or not value.strip():
            raise serializers.ValidationError("กรุณาระบุ Action Log")
        return value.strip()
        
    def validate_user_name(self, value):
        """ตรวจสอบว่า user_name ไม่เป็นค่าว่าง"""
        if not value or not value.strip():
            raise serializers.ValidationError("กรุณาระบุชื่อผู้ใช้")
        return value.strip()
    
    
class MemberRegistrationSerializer(serializers.Serializer):
    """
    Serializer สำหรับการสมัครสมาชิก Member ใหม่
    รองรับฟิลด์ทั้งหมดของ TcdAppMember และ OTP token validation
    """
    # Required fields
    username = serializers.CharField(max_length=50, min_length=4)
    email = serializers.EmailField(max_length=50)
    password = serializers.CharField(write_only=True, max_length=50, min_length=4)
    confirm_password = serializers.CharField(write_only=True, max_length=50, min_length=4)
    first_name = serializers.CharField(max_length=255)
    last_name = serializers.CharField(max_length=255)
    
    # Required basic fields (updated phone to be required)
    phone = serializers.CharField(max_length=50)
    
    # Optional basic fields
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    identity_card_no = serializers.CharField(max_length=20, required=False, allow_blank=True, allow_null=True)
    website = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    
    # Social media IDs
    fb_id = serializers.CharField(max_length=50, required=False, allow_blank=True, allow_null=True)
    google_id = serializers.CharField(max_length=50, required=False, allow_blank=True, allow_null=True)
    apple_id = serializers.CharField(max_length=50, required=False, allow_blank=True, allow_null=True)
    
    # Master data foreign keys
    app_mas_member_type_id = serializers.IntegerField(required=False, allow_null=True)
    app_mas_government_sector_id = serializers.IntegerField(required=False, allow_null=True)
    app_mas_ministry_id = serializers.IntegerField(required=False, allow_null=True)
    app_mas_department_id = serializers.IntegerField(required=False, allow_null=True)
    
    # Other text fields
    app_mas_government_sector_other = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    app_mas_ministry_other = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    app_mas_department_other = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    
    # Settings
    is_notification = serializers.CharField(max_length=1)
    lang = serializers.CharField(max_length=2, default='th')
    status = serializers.CharField(max_length=1, default='1')
    
    # OTP validation
    otp_token = serializers.CharField(required=True, help_text="Token ที่ได้จากการ verify OTP")
    
    def validate_username(self, value):
        """ตรวจสอบ username: pattern (a-z, A-Z, 0-9, @, _, -, .), min length 4, check duplicates"""
        import re
        
        # ตรวจสอบ pattern: a-z, A-Z, 0-9, @, _, -, .
        if not re.match(r'^[a-zA-Z0-9@_.-]+$', value):
            raise serializers.ValidationError("ชื่อผู้ใช้สามารถใช้ได้เฉพาะตัวอักษร ตัวเลข และสัญลักษณ์ @ _ - . เท่านั้น")
        
        # ตรวจสอบความซ้ำใน TcdAppMember
        if TcdAppMember.objects.filter(username=value).exists():
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2013, language=language)  # Username already exists
            raise serializers.ValidationError(error_response.get('error_message', 'ชื่อผู้ใช้นี้มีในระบบแล้ว'))
        
        # ตรวจสอบความซ้ำใน TcdUserConsult
        if TcdUserConsult.objects.filter(username=value).exists():
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2013, language=language)  # Username already exists
            raise serializers.ValidationError(error_response.get('error_message', 'ชื่อผู้ใช้นี้มีในระบบแล้ว'))
        
        return value
    
    def validate_email(self, value):
        """ตรวจสอบ email: required, email pattern, check duplicates"""
        # ตรวจสอบความซ้ำใน TcdAppMember
        if TcdAppMember.objects.filter(email=value).exists():
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2011, language=language)  # Email already exists
            raise serializers.ValidationError(error_response.get('error_message', 'อีเมลนี้มีในระบบแล้ว'))
        
        # ตรวจสอบความซ้ำใน TcdUserConsult
        if TcdUserConsult.objects.filter(email=value).exists():
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2011, language=language)  # Email already exists
            raise serializers.ValidationError(error_response.get('error_message', 'อีเมลนี้มีในระบบแล้ว'))
        
        return value
    
    def validate_phone(self, value):
        """ตรวจสอบ phone: required, numbers only, exactly 10 digits, check duplicates"""
        import re
        
        # ตรวจสอบว่าเป็นตัวเลขเท่านั้น และมี 10 หลักพอดี
        if not re.match(r'^[0-9]{10}$', value):
            raise serializers.ValidationError("เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลักเท่านั้น")
        
        # ตรวจสอบความซ้ำใน TcdAppMember
        if TcdAppMember.objects.filter(phone=value).exists():
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2012, language=language)  # Phone already exists
            raise serializers.ValidationError(error_response.get('error_message', 'เบอร์โทรศัพท์นี้มีในระบบแล้ว'))
        
        # ตรวจสอบความซ้ำใน TcdUserConsult
        if TcdUserConsult.objects.filter(phone=value).exists():
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2012, language=language)  # Phone already exists
            raise serializers.ValidationError(error_response.get('error_message', 'เบอร์โทรศัพท์นี้มีในระบบแล้ว'))
        
        return value
    
    def validate_identity_card_no(self, value):
        """ตรวจสอบว่า identity_card_no ไม่ซ้ำ (ถ้ามี)"""
        if value and TcdAppMember.objects.filter(identity_card_no=value).exists():
            # ใช้ service_error_response เพื่อ i18n
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2007, language=language)  # Data duplicate
            raise serializers.ValidationError(error_response.get('error_message', 'หมายเลขบัตรประชาชนนี้มีในระบบแล้ว'))
        return value
    
    def validate_otp_token(self, value):
        """ตรวจสอบ OTP token ที่ผ่านการ verify แล้ว"""
        from authentication.services.otp_service import OTPService
        
        validation_result = OTPService.validate_verified_token(value)
        if not validation_result['success']:
            # ใช้ service_error_response เพื่อ i18n
            # ใช้ error_code 2025 สำหรับ OTP token ไม่ถูกต้อง
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2025, language=language)
            
            raise serializers.ValidationError(
                error_response.get('error_message', validation_result.get('message', 'OTP token ไม่ถูกต้องหรือหมดอายุ'))
            )
        
        # เก็บข้อมูล OTP ไว้ใช้ใน validate method
        self._otp_data = validation_result['data']
        return value
    
    def validate(self, attrs):
        """ตรวจสอบข้อมูลรวม"""
        # ตรวจสอบว่า password และ confirm_password ตรงกัน
        password = attrs.get('password')
        confirm_password = attrs.get('confirm_password')
        
        if password != confirm_password:
            language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
            error_response = service_error_response(error_code=2016, language=language)
            raise serializers.ValidationError(error_response.get('error_message', 'รหัสผ่านและการยืนยันรหัสผ่านไม่ตรงกัน'))
        
        # ตรวจสอบว่า OTP email ตรงกับ email ที่ลงทะเบียน
        if hasattr(self, '_otp_data'):
            otp_email = self._otp_data.get('identifier')
            registration_email = attrs.get('email')
            
            if otp_email != registration_email:
                # ใช้ service_error_response เพื่อ i18n
                language = getattr(self.context.get('request'), 'language', 'th') if self.context.get('request') else 'th'
                error_response = service_error_response(error_code=2029, language=language)  # Email mismatch
                raise serializers.ValidationError({
                    'email': error_response.get('error_message', 'อีเมลที่ลงทะเบียนต้องตรงกับอีเมลที่ใช้ในการ verify OTP')
                })
        
        return attrs


# Master Data Serializers

class TcdAppMasMemberTypeSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล Member Type
    """
    
    class Meta:
        model = TcdAppMasMemberType
        fields = ['id', 'name_th', 'name_en']
        read_only_fields = ['id']


class TcdAppMasGovernmentSectorSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล Government Sector
    """
    
    class Meta:
        model = TcdAppMasGovernmentSector
        fields = ['id', 'name_th', 'name_en']
        read_only_fields = ['id']


class TcdAppMasMinistrySerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล Ministry
    """
    
    class Meta:
        model = TcdAppMasMinistry
        fields = ['id', 'app_mas_government_sector_id', 'name_th', 'name_en']
        read_only_fields = ['id']


class TcdAppMasDepartmentSerializer(serializers.ModelSerializer):
    """
    Serializer สำหรับข้อมูล Department
    """

    class Meta:
        model = TcdAppMasDepartment
        fields = ['id', 'app_mas_ministry_id', 'name_th', 'name_en']
        read_only_fields = ['id']


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer สำหรับการขอรีเซ็ตรหัสผ่าน
    """
    email = serializers.EmailField(
        required=True,
        help_text="อีเมลของผู้ใช้ที่ต้องการรีเซ็ตรหัสผ่าน"
    )

    def validate_email(self, value):
        """ตรวจสอบว่าอีเมลมีอยู่ในระบบหรือไม่"""
        # ตรวจสอบใน TcdAppMember
        member_exists = TcdAppMember.objects.filter(email=value).exists()
        # ตรวจสอบใน TcdUserConsult
        consultant_exists = TcdUserConsult.objects.filter(email=value).exists()

        if not member_exists and not consultant_exists:
            raise serializers.ValidationError("ไม่พบอีเมลนี้ในระบบ")

        return value



class PasswordResetVerifyOTPSerializer(serializers.Serializer):
    """
    Serializer สำหรับการตรวจสอบ OTP ในการรีเซ็ตรหัสผ่าน
    """
    email = serializers.EmailField(
        required=True,
        help_text="อีเมลของผู้ใช้"
    )
    otp = serializers.CharField(
        required=True,
        min_length=6,
        max_length=6,
        help_text="รหัส OTP 6 หลัก"
    )
    ref_code = serializers.CharField(
        required=True,
        min_length=6,
        max_length=6,
        help_text="รหัสอ้างอิง 6 หลัก"
    )
    otp_token = serializers.CharField(
        required=True,
        help_text="Token จากการขอ OTP"
    )

    def validate_email(self, value):
        """ตรวจสอบว่าอีเมลมีอยู่ในระบบหรือไม่"""
        # ตรวจสอบใน TcdAppMember
        member_exists = TcdAppMember.objects.filter(email=value).exists()
        # ตรวจสอบใน TcdUserConsult
        consultant_exists = TcdUserConsult.objects.filter(email=value).exists()

        if not member_exists and not consultant_exists:
            raise serializers.ValidationError("ไม่พบอีเมลนี้ในระบบ")

        return value


class PasswordResetUpdatePasswordSerializer(serializers.Serializer):
    """
    Serializer สำหรับการอัปเดตรหัสผ่านใหม่
    """
    email = serializers.EmailField(
        required=True,
        help_text="อีเมลของผู้ใช้"
    )
    new_password = serializers.CharField(
        required=True,
        min_length=4,
        max_length=50,
        write_only=True,
        help_text="รหัสผ่านใหม่"
    )
    confirm_password = serializers.CharField(
        required=True,
        min_length=4,
        max_length=50,
        write_only=True,
        help_text="ยืนยันรหัสผ่านใหม่"
    )
    verified_token = serializers.CharField(
        required=True,
        help_text="Token ที่ได้จากการ verify OTP สำเร็จ"
    )

    def validate(self, attrs):
        """ตรวจสอบข้อมูลทั้งหมด"""
        new_password = attrs.get('new_password')
        confirm_password = attrs.get('confirm_password')

        # ตรวจสอบว่ารหัสผ่านตรงกัน
        if new_password != confirm_password:
            raise serializers.ValidationError("รหัสผ่านใหม่และการยืนยันรหัสผ่านไม่ตรงกัน")

        return attrs

    def validate_email(self, value):
        """ตรวจสอบว่าอีเมลมีอยู่ในระบบหรือไม่"""
        # ตรวจสอบใน TcdAppMember
        member_exists = TcdAppMember.objects.filter(email=value).exists()
        # ตรวจสอบใน TcdUserConsult
        consultant_exists = TcdUserConsult.objects.filter(email=value).exists()

        if not member_exists and not consultant_exists:
            raise serializers.ValidationError("ไม่พบอีเมลนี้ในระบบ")

        return value


class ChangePasswordSerializer(serializers.Serializer):
    """
    Serializer สำหรับการเปลี่ยนรหัสผ่าน (ไม่ต้องผ่าน OTP)
    """
    current_password = serializers.CharField(
        required=True,
        min_length=4,
        max_length=50,
        write_only=True,
        help_text="รหัสผ่านปัจจุบัน"
    )
    new_password = serializers.CharField(
        required=True,
        min_length=4,
        max_length=50,
        write_only=True,
        help_text="รหัสผ่านใหม่"
    )
    confirm_password = serializers.CharField(
        required=True,
        min_length=4,
        max_length=50,
        write_only=True,
        help_text="ยืนยันรหัสผ่านใหม่"
    )

    def validate(self, attrs):
        """ตรวจสอบข้อมูลทั้งหมด"""
        new_password = attrs.get('new_password')
        confirm_password = attrs.get('confirm_password')
        current_password = attrs.get('current_password')

        # ตรวจสอบว่ารหัสผ่านใหม่และการยืนยันตรงกัน
        if new_password != confirm_password:
            raise serializers.ValidationError("รหัสผ่านใหม่และการยืนยันรหัสผ่านไม่ตรงกัน")

        # ตรวจสอบว่ารหัสผ่านใหม่ไม่เหมือนรหัสผ่านเก่า
        if new_password == current_password:
            raise serializers.ValidationError("รหัสผ่านใหม่ต้องไม่เหมือนกับรหัสผ่านปัจจุบัน")

        return attrs


class UpdateMemberInfoSerializer(serializers.Serializer):
    """
    Serializer สำหรับการอัปเดตข้อมูลสมาชิก Member พร้อมการตรวจสอบรหัสผ่าน
    """
    # Required password verification
    current_password = serializers.CharField(
        required=True,
        min_length=4,
        max_length=50,
        write_only=True,
        help_text="รหัสผ่านปัจจุบันสำหรับยืนยันตัวตน"
    )
    
    # Optional fields that can be updated
    name = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    first_name = serializers.CharField(max_length=255, required=False)
    last_name = serializers.CharField(max_length=255, required=False)
    email = serializers.EmailField(max_length=50, required=False)
    phone = serializers.CharField(max_length=50, required=False)
    identity_card_no = serializers.CharField(max_length=20, required=False, allow_blank=True, allow_null=True)
    website = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    
    # Social media IDs
    fb_id = serializers.CharField(max_length=50, required=False, allow_blank=True, allow_null=True)
    google_id = serializers.CharField(max_length=50, required=False, allow_blank=True, allow_null=True)
    apple_id = serializers.CharField(max_length=50, required=False, allow_blank=True, allow_null=True)
    
    # Master data foreign keys
    app_mas_member_type_id = serializers.IntegerField(required=False, allow_null=True)
    app_mas_government_sector_id = serializers.IntegerField(required=False, allow_null=True)
    app_mas_ministry_id = serializers.IntegerField(required=False, allow_null=True)
    app_mas_department_id = serializers.IntegerField(required=False, allow_null=True)
    
    # Other text fields
    app_mas_government_sector_other = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    app_mas_ministry_other = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    app_mas_department_other = serializers.CharField(max_length=255, required=False, allow_blank=True, allow_null=True)
    
    # Settings
    is_notification = serializers.CharField(max_length=1, required=False)
    lang = serializers.CharField(max_length=2, required=False)
    
    def validate_email(self, value):
        """ตรวจสอบว่าอีเมลไม่ซ้ำกับสมาชิกคนอื่น"""
        if value:
            # ดึง user_id จาก context
            request = self.context.get('request')
            if request and hasattr(request, 'user') and hasattr(request.user, 'id'):
                user_id = request.user.id
                # ตรวจสอบว่าอีเมลนี้ถูกใช้โดยสมาชิกคนอื่นหรือไม่
                existing_member = TcdAppMember.objects.filter(email=value).exclude(id=user_id).first()
                if existing_member:
                    raise serializers.ValidationError("อีเมลนี้ถูกใช้งานแล้วโดยสมาชิกคนอื่น")
        return value
    
    def validate_phone(self, value):
        """ตรวจสอบว่าเบอร์โทรไม่ซ้ำกับสมาชิกคนอื่น"""
        if value:
            # ตรวจสอบรูปแบบเบอร์โทร (เฉพาะตัวเลข 10 หลัก)
            import re
            if not re.match(r'^\d{10}$', value):
                raise serializers.ValidationError("เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลักเท่านั้น")
            
            # ดึง user_id จาก context
            request = self.context.get('request')
            if request and hasattr(request, 'user') and hasattr(request.user, 'id'):
                user_id = request.user.id
                # ตรวจสอบว่าเบอร์โทรนี้ถูกใช้โดยสมาชิกคนอื่นหรือไม่
                existing_member = TcdAppMember.objects.filter(phone=value).exclude(id=user_id).first()
                if existing_member:
                    raise serializers.ValidationError("เบอร์โทรศัพท์นี้ถูกใช้งานแล้วโดยสมาชิกคนอื่น")
        return value
    
    def validate_identity_card_no(self, value):
        """ตรวจสอบว่าหมายเลขบัตรประชาชนไม่ซ้ำกับสมาชิกคนอื่น"""
        if value and value.strip():
            # ตรวจสอบรูปแบบเลขบัตรประชาชน (13 หลัก)
            import re
            if not re.match(r'^\d{13}$', value.strip()):
                raise serializers.ValidationError("หมายเลขบัตรประชาชนต้องเป็นตัวเลข 13 หลักเท่านั้น")
            
            # ดึง user_id จาก context
            request = self.context.get('request')
            if request and hasattr(request, 'user') and hasattr(request.user, 'id'):
                user_id = request.user.id
                # ตรวจสอบว่าหมายเลขบัตรประชาชนนี้ถูกใช้โดยสมาชิกคนอื่นหรือไม่
                existing_member = TcdAppMember.objects.filter(identity_card_no=value.strip()).exclude(id=user_id).first()
                if existing_member:
                    raise serializers.ValidationError("หมายเลขบัตรประชาชนนี้ถูกใช้งานแล้วโดยสมาชิกคนอื่น")
        return value
    
    def validate_app_mas_member_type_id(self, value):
        """ตรวจสอบว่า Member Type ID มีอยู่จริง"""
        if value:
            if not TcdAppMasMemberType.objects.filter(id=value).exists():
                raise serializers.ValidationError("ไม่พบประเภทสมาชิกที่ระบุ")
        return value
    
    def validate_app_mas_government_sector_id(self, value):
        """ตรวจสอบว่า Government Sector ID มีอยู่จริง"""
        if value:
            if not TcdAppMasGovernmentSector.objects.filter(id=value).exists():
                raise serializers.ValidationError("ไม่พบหน่วยงานภาครัฐที่ระบุ")
        return value
    
    def validate_app_mas_ministry_id(self, value):
        """ตรวจสอบว่า Ministry ID มีอยู่จริง"""
        if value:
            if not TcdAppMasMinistry.objects.filter(id=value).exists():
                raise serializers.ValidationError("ไม่พบกระทรวงที่ระบุ")
        return value
    
    def validate_app_mas_department_id(self, value):
        """ตรวจสอบว่า Department ID มีอยู่จริง"""
        if value:
            if not TcdAppMasDepartment.objects.filter(id=value).exists():
                raise serializers.ValidationError("ไม่พบกรมที่ระบุ")
        return value
    
    def validate_is_notification(self, value):
        """ตรวจสอบค่า is_notification"""
        if value and value not in ['Y', 'N']:
            raise serializers.ValidationError("ค่าการแจ้งเตือนต้องเป็น 'Y' หรือ 'N' เท่านั้น")
        return value
    
    def validate_lang(self, value):
        """ตรวจสอบค่าภาษา"""
        if value and value not in ['th', 'en']:
            raise serializers.ValidationError("ภาษาต้องเป็น 'th' หรือ 'en' เท่านั้น")
        return value


class UpdateMemberLangSerializer(serializers.Serializer):
    """
    Serializer สำหรับการอัปเดตภาษาของสมาชิก Member โดยไม่ต้องตรวจสอบรหัสผ่าน
    """
    lang = serializers.CharField(
        max_length=2, 
        required=True,
        help_text="ภาษาที่ต้องการเปลี่ยน ('th' หรือ 'en')"
    )
    
    def validate_lang(self, value):
        """ตรวจสอบค่าภาษา"""
        if value not in ['th', 'en']:
            raise serializers.ValidationError("ภาษาต้องเป็น 'th' หรือ 'en' เท่านั้น")
        return value


class StaffLoginSerializer(serializers.Serializer):
    """
    Serializer สำหรับการเข้าสู่ระบบของ Staff (TcdUsers)
    """
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, style={'input_type': 'password'})
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        logger.info(f"StaffLoginSerializer.validate called for username: {username}")
        
        if username and password:
            try:
                # Import TcdUsers here to avoid circular import
                from MCDC.models import TcdUsers
                
                # ค้นหา staff จากตาราง tcd_users
                staff = TcdUsers.objects.get(username=username)
                logger.info(f"Found staff: {username}")
                
                # ตรวจสอบรหัสผ่าน - เข้ารหัสด้วย MD5 แล้วเปรียบเทียบ
                logger.info(f"Verifying password for staff: {username}")
                if verify_password(password, staff.password):
                    logger.info(f"Password verification successful for staff: {username}")
                    attrs['staff'] = staff
                    return attrs
                else:
                    logger.warning(f"Password verification failed for staff: {username}")
                    attrs['error_code'] = 1001  # Invalid username or password
                    return attrs
                    
            except TcdUsers.DoesNotExist:
                logger.warning(f"Staff not found: {username}")
                attrs['error_code'] = 1001  # Invalid username or password
                return attrs
            except Exception as e:
                logger.error(f"Unexpected error in StaffLoginSerializer.validate: {str(e)}")
                attrs['error_code'] = 1001  # Invalid username or password
                return attrs
        else:
            logger.warning("Missing username or password")
            attrs['error_code'] = 2001  # Missing credentials
            return attrs


class DeleteMemberSerializer(serializers.Serializer):
    """
    Serializer สำหรับการลบบัญชีสมาชิก Member พร้อมการตรวจสอบรหัสผ่าน
    """
    current_password = serializers.CharField(
        required=True,
        min_length=4,
        max_length=50,
        write_only=True,
        help_text="รหัสผ่านปัจจุบันสำหรับยืนยันตัวตน"
    )

